<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="tss_phase_description_no_activity_data">No activity data</string>
    <string name="tss_phase_description_too_easy_description">In this phase training is too easy and you are losing fitness. To be fresh to a race, target this phase.</string>
    <string name="tss_phase_description_maintaining_fitness_description">Staying in this phase you maintain your fitness. To improve your fitness, visit the productive training phase once in a while.</string>
    <string name="tss_phase_description_productive_training_description">For most athletes this is the area where your training is most productive. Your fitness and fatigue is increasing in appropriate portion.</string>
    <string name="tss_phase_description_going_too_hard_description">In this area the risk of illness and injury increases significantly.</string>

    <string name="tss_fitness_ctl_label">Fitness (CTL)</string>
    <string name="tss_fatigue_atl_label">Fatigue (ATL)</string>
    <string name="tss_form_tsb_label">Form (TSB)</string>
    <string name="tss_fitness_trend_ctl_label">Fitness trend(CTL)</string>
    <string name="tss_form_tss_d_label">Form(TSS/d)</string>

    <string name="tss_info_panel_fitness">Fitness</string>
    <string name="tss_info_panel_fatigue">Fatigue</string>
    <string name="tss_info_panel_form">Form</string>
    <string name="tss_info_panel_tss">TSS</string>

    <string name="tss_fitness_and_fatigue_graph_caption">FITNESS AND FATIGUE</string>
    <string name="tss_fatigue_graph_caption">Fatigue</string>
    <string name="tss_fitness_graph_caption">Fitness</string>
    <string name="tss_form_graph_caption">Form</string>
    <string name="tss_graph_y_axis_label">TSS/d</string>
    <string name="tss_read_more_about_tss_title">Learn more about TSS</string>
    <string name="tss_read_more_about_tss_values_description">Training Stress Score® (TSS) is used to quantify the training stress of a workout.</string>
    <string name="tss_read_more_about_training_progression_title">Learn more about progress graphs</string>
    <string name="tss_read_more_about_training_progression_description">Suunto app helps you understand and manage your training load.</string>
    <string name="tss_form_insight_losing_fitness">You should feel recovered now. To see progress, get back to your training schedule. If you are targeting a race today or tomorrow, good luck!</string>
    <string name="tss_form_insight_losing_fitness_4_days_or_more">You’re not improving your fitness. When the time is right, try returning to your normal daily training schedule. If you’ve been sick, be careful with your return to training.</string>
    <string name="tss_form_insight_maintaining_fitness">You’re maintaining your fitness. If you want to improve your fitness, try increasing the volume or intensity of your training.</string>
    <string name="tss_form_insight_maintaining_fitness_slow_improvement">Good job! You’re slowly improving your fitness. If you want to improve and be on productive training phase, try increasing the volume or intensity of your training.</string>
    <string name="tss_form_insight_productive_training">Your training volume or intensity has been higher than previous weeks. Keep up the good work! Remember to recover after heavy training days.</string>
    <string name="tss_form_insight_productive_training_5_days">Your training load has been high lately, with increased duration or high intensity. Remember that improving your fitness requires time to recover.</string>
    <string name="tss_form_insight_productive_training_10_days">Your training has been progressing for a while now! Remember to rest between heavy weeks.</string>
    <string name="tss_form_insight_productive_training_14_days">Your training has been heavy for a while. Risk of injury or illness increases if you don’t get enough rest.</string>
    <string name="tss_form_insight_going_too_hard">You’re making a strong effort, but make sure you don’t increase your training load too fast. Get some rest before returning to your normal training schedule.</string>
    <string name="tss_analysis_training_progress">Training progress</string>
    <string name="tss_analysis_vo2max_running">Running VO₂max</string>
    <string name="tss_analysis_vo2max">VO₂max</string>
    <string name="tss_analysis_vo2max_no_data">No data</string>
    <string name="tss_analysis_fitness_age">Fitness age</string>
    <string name="tss_analysis_vo2max_state_superior">Superior</string>
    <string name="tss_analysis_vo2max_state_excellent">Excellent</string>
    <string name="tss_analysis_vo2max_state_good">Good</string>
    <string name="tss_analysis_vo2max_state_fair">Fair</string>
    <string name="tss_analysis_vo2max_state_poor">Poor</string>
    <string name="tss_analysis_vo2max_state_very_poor">Very poor</string>
    <string name="tss_analysis_vo2max_description_md">
        # Running VO₂max\n
        Suunto estimates your maximal oxygen uptake (VO₂max) from running and walking workouts by analyzing your heart rate and pace data.\n\n

        # Track your progress\n
        A higher VO₂max indicates stronger cardiovascular function and more efficient oxygen utilization, offering valuable insights into your heart and lung health.\n\n

        Tracking VO₂max over time helps you monitor aerobic fitness. Beginners may see rapid improvements, while progress tends to be slower for well-trained individuals.\n\n

        For runners, VO₂max also reflects running efficiency—better technique can lead to improved VO₂max estimates.\n\n
    </string>

    <string name="tss_training_peaks_logo_content_description">TrainingPeaks</string>
    <string name="tss_training_peaks_algorithm_notice">Metrics powered by TrainingPeaks.</string>
    <string name="tss_value_explanations_title">Training progression values explained</string>
    <string name="tss_value_explanations_read_more_link">Read more</string>
    <string name="tss_value_explanations_fitness_title">Fitness (CTL)</string>
    <string name="tss_value_explanations_fitness_description">Fitness (Chronic Training Load) describes your long-term training load. It gives you a trend line of where your fitness is going in the long term.</string>
    <string name="tss_value_explanations_fatigue_title">Fatigue (ATL)</string>
    <string name="tss_value_explanations_fatigue_description">Fatigue (Acute Training Load) describes your short-term training load: how your recent workouts have been. If you train more than usual, your Fatigue will increase faster than your Fitness.</string>
    <string name="tss_value_explanations_form_title">Form (TSB)</string>
    <string name="tss_value_explanations_form_description">Form (Training Stress Balance) is about workload. It compares your long-term training (CTL) and short-term training (ATL) to show you how adapted you are to your training load.</string>
    <string name="tss_value_explanations_tss_title">TSS</string>
    <string name="tss_value_explanations_tss_description">Every activity gets a Training Stress Score® (TSS). It\'s based on the intensity and duration of your activity and it tells you how hard that particular activity was.\nTSS (r) - for running pace based calculation\nTSS (hr) - for heart rate based calculations\nTSS (p) - for power based calculations\nTSS (s) - for swimming pace based calculations\nTSS (MET) - for MET based calculations\nTSS (Manual) - after user has edited the value</string>

    <string name="progress_fitness_change">Fitness change</string>
    <string name="progress_suunto_coach">Suunto coach</string>
    <string name="progress_suunto_coach_no_data">Track workouts to get insights.</string>
    <string name="progress_explanation_ctl">Fitness Level (CTL) represents your long-term training foundation, calculated from the weighted average of daily TSS® over the past 42 days, reflecting endurance and capability.</string>
    <string name="progress_explanation_atl">Fatigue Level (ATL) indicates recent training load based on the past 7 days of TSS®, showing short-term training impacts.</string>
    <string name="progress_explanation_tsb">Fitness Status (TSB) is the difference between CTL and ATL, indicating recovery.</string>
    <string name="progress_explanation_tsb_ranges">Reference Ranges:\n • TSB &lt; -30: Too high intensity\n • -30 ≤ TSB &lt; -10: Fatigue/Improving fitness\n • -10 ≤ TSB &lt; 15: Training balance\n • TSB ≥ 15: Losing fitness or recovering</string>
    <string name="progress_explanation_tss">Training Stress Score® (TSS®) quantifies each activity\'s load based on intensity and duration.</string>
    <string name="progress_explanation_summary">In summary, TSS® quantifies training load, while CTL and ATL help assess training status for better guidance.</string>
    <string name="progress_ctl_ramp_rate">ramp rate</string>
    <string name="progress_6_weeks_range">6 weeks range</string>
    <string name="progress_6_months_range">6 months range</string>
    <plurals name="progress_x_days_this_week">
        <item quantity="one">%d day this week</item>
        <item quantity="other">%d days this week</item>
    </plurals>
    <plurals name="progress_x_days_this_month">
        <item quantity="one">%d day this month</item>
        <item quantity="other">%d days this month</item>
    </plurals>
    <plurals name="progress_x_days_this_year">
        <item quantity="one">%d day this year</item>
        <item quantity="other">%d days this year</item>
    </plurals>

    <string name="progress_latest_vo2max">Latest VO₂max</string>
    <string name="progress_tab_title">Progress</string>

    <string name="progress_what_is_vo2max_title">What is VO₂max?</string>
    <string name="progress_what_is_vo2max_description">VO₂max (maximum oxygen uptake) measures how much oxygen your body can use during intense exercise. It is a key indicator of cardiovascular fitness and endurance—higher values generally mean better performance. VO₂max is influenced by factors such as age, gender, and training level. Improving it can enhance overall athletic ability.</string>
    <string name="progress_how_to_measure_vo2max_title">How to measure VO₂max?</string>
    <string name="progress_how_to_measure_vo2max_description">How to get VO₂max value? Your watch estimates VO₂max by analyzing data from your running workouts, including heart rate and pace. You can also assess your VO₂max capacity using the "VO₂max Test (Cooper Test)" in SuuntoPlus.</string>
    <string name="progress_how_to_use_vo2max_title">How to use VO₂max?</string>
    <string name="progress_how_to_use_vo2max_description">Health indicator: A higher VO₂max typically reflects better cardiovascular function and oxygen delivery. Tracking this metric provides insights into your heart and lung health, helping you determine if you need more aerobic training or lifestyle adjustments. Benchmark comparison: Compare your VO₂max with reference standards to evaluate your aerobic capacity and fitness level. Long-term gauge: Regular VO₂max measurements over months or years help track fitness trends—whether improving, stabilizing, or declining. Identifying patterns early allows you to adjust your training volume and intensity to stay on track with your goals.</string>
    
    <string name="buy_premium_popup_tss_analysis_description">The long-term analysis view tracks your training load and helps you stay in balance, whether that means progress, maintaining your fitness or avoiding over-training.</string>

    <string name="graph_data_unit_hrv_avg_daily">avg daily HRV</string>
    <string name="graph_data_unit_hrv_daily">daily HRV</string>

    <!-- Diary graph data types -->
    <string name="graph_type_tss">TSS</string>
    <string name="graph_type_exercise_feel">Exercise feel</string>
    <string name="graph_data_unit_hours">hours</string>

    <string name="graph_type_duration">Duration</string>
    <string name="graph_type_distance">Distance</string>
    <string name="graph_type_steps">Steps</string>
    <string name="graph_type_calories">Calories</string>
    <string name="graph_type_sleep_quality">Sleep quality</string>
    <string name="graph_type_average_heart_rate">HR in workouts</string>
    <string name="graph_type_ascent">Ascent</string>
    <string name="graph_type_fitness_level">Fitness level</string>
    <string name="graph_type_sleep_duration">Sleep duration</string>
    <string name="graph_type_sleep_reqularity">Sleep regularity</string>
    <string name="graph_type_blood_oxygen">Blood oxygen</string>
    <string name="graph_type_training">Training</string>
    <string name="graph_type_avg_hr_during_sleep">Avg. sleep HR</string>
    <string name="graph_type_morning_resources">Wake-up resources</string>
    <string name="graph_type_free_dive_count">Total freedives</string>
    <string name="graph_type_scuba_dive_count">Total scuba dives</string>
    <string name="recovery_tab_hrv_trend">HRV trend</string>

    <string name="graph_type_total_duration">Total time</string>
    <string name="graph_type_nap_duration">Nap time</string>
    <string name="graph_type_avg_speed">Avg. Speed</string>
    <string name="graph_type_avg_pace">Avg. Pace</string>
    <string name="graph_type_avg_power">Avg. Power</string>
    <string name="graph_type_normalized_power">NP®</string>
    <string name="graph_type_avg_swim_pace">Avg. Swim Pace</string>
    <!-- END Diary graph data types -->

    <!-- Diary graph data units -->
    <string name="graph_data_unit_distance_km">kilometers</string>
    <string name="graph_data_unit_distance_mi">miles</string>
    <string name="graph_data_unit_steps_daily">daily steps</string>
    <string name="graph_data_unit_steps_avg_daily">avg daily steps</string>
    <string name="graph_data_unit_calories_daily">daily calories</string>
    <string name="graph_data_unit_calories_avg_daily">avg daily calories</string>
    <string name="graph_data_unit_avg_percent">avg %</string>
    <string name="graph_data_unit_total_daily">total daily</string>
    <string name="graph_data_unit_total_weekly">total weekly</string>
    <string name="graph_data_unit_total_monthly">total monthly</string>
    <string name="graph_data_unit_total_yearly">total yearly</string>
    <string name="graph_data_unit_exercise_feeling">avg feeling</string>
    <string name="graph_data_unit_avg_hr">avg bpm</string>
    <string name="graph_data_unit_fitness_level">VO₂max</string>
    <string name="graph_data_unit_avg_daily">daily avg</string>
    <string name="graph_data_unit_daily_hours">daily hours</string>
    <string name="graph_data_unit_dives">Dives</string>
    <string name="graph_data_unit_avg_speed">avg speed</string>
    <string name="graph_data_unit_avg_pace">avg pace</string>
    <string name="graph_data_unit_avg_power">avg power</string>
    <string name="graph_data_unit_normalized_power">NP®</string>
    <string name="graph_data_unit_avg_swim_pace">avg swim pace</string>
    <!-- END Diary graph data units -->

    <!-- Training zone Summary -->
    <string name="training_zone_summary_filter_sports_title">Sports</string>
    <string name="training_zone_summary_filter_sports_all">All</string>
    <string name="training_zone_summary_filter_grouping_title">Group by</string>
    <string name="training_zone_summary_filter_grouping_weekly">Week</string>
    <string name="training_zone_summary_filter_grouping_monthly">Month</string>
    <string name="training_zone_summary_filter_grouping_yearly">Year</string>
    <string name="training_zone_summary_filter_grouping_by_activity">Activity</string>
    <string name="training_zone_summary_filter_reset">Reset</string>
    <string name="training_zone_summary_filter_done">Done</string>
    <string name="training_zone_summary_filtering">Settings</string>
    <string name="training_zone_summary_activity_picker_title">Select Sport (%1$s)</string>
    <string name="training_zone_summary_activity_picker_cancel">Cancel</string>
    <string name="training_zone_summary_activity_picker_ok">OK</string>
    <string name="training_zone_summary_activity_picker_search">Search</string>
    <string name="training_zone_summary_no_data_title">Compare your workouts</string>
    <string name="training_zone_summary_no_data">Summary helps you in tracking your training frequency, volume and intensity. Complete your first activity to see data.</string>
    <string name="training_zone_summary_no_matched_data_for_selected_filter">There\'s no data that fits all the criteria you\'ve selected. Adjust the filters or reset them to expand your search.</string>
    <string name="training_zone_summary_empty_state_reset_filters">Reset filters</string>
    <string name="training_zone_summary_filter_date_all">All</string>
    <string name="training_zone_summary_filter_date_title">Date range</string>
    <string name="training_zone_summary_filter_date_start">Start date</string>
    <string name="training_zone_summary_filter_date_end">End date</string>
    <string name="training_zone_summary_total">Total</string>
    <string name="training_zone_summary_filter_distance">Distance</string>
    <string name="training_zone_summary_filter_distance_all">All</string>
    <string name="training_zone_summary_filter_distance_select_sport_with_distance">Choose a sport with distance data to enable this filter</string>
    <string name="training_zone_summary_tags_picker_title">Select tag (%1$s)</string>
    <string name="training_zone_summary_tags_picker_recent_section_title">Recent</string>
    <string name="training_zone_summary_tags_picker_automatic_tags_section_title">Automatic tags</string>
    <string name="training_zone_summary_tags_picker_custom_tags_section_title">Custom tags</string>
    <string name="training_zone_summary_filter_tags_title">Tags</string>
    <string name="training_zone_summary_filter_no_tags_selected">None</string>
    <string name="training_zone_summary_grouped_weeks_count">%d weeks</string>
    <string name="training_zone_summary_grouped_months_count">%d months</string>
    <string name="training_zone_summary_grouped_years_count">%d years</string>
    <string name="training_zone_summary_no_data_in_details">No workouts found for the selected dates.</string>
    <string name="training_zone_summary_show_empty_rows">Show empty rows</string>
    <string name="training_zone_summary_filter_layout_title">Layout</string>
    <string name="training_zone_summary_filter_layout_type_graph">Graph</string>
    <string name="training_zone_summary_filter_layout_type_table">Table</string>
    <string name="buy_premium_popup_summary_description">How many kilometers did you cover last season, and what was your longest run? Monitor workout volume, pace and heart rate seamlessly. Customize your view by date or distance—yearly, weekly, monthly, or by activity. Gain insights to optimize your training and achieve goals effortlessly.</string>
    <!-- END of Training zone Summary -->

    <!-- Training V2 -->
    <string name="training_v2_intensity_zones_title">Intensity zones</string>
    <string name="training_v2_training_volume_title">Training volume</string>
    <string name="training_v2_training_all_sports">All sports</string>
    <string name="training_v2_training_multiple_sports">Multiple</string>
    <string name="training_v2_training_data_analysis">Data analysis</string>
    <string name="training_v2_select_sports">Select sports</string>
    <plurals name="training_v2_training_data_analysis_count_workouts">
        <item quantity="one">%1$d activity</item>
        <item quantity="few">%1$d activities</item>
        <item quantity="many">%1$d activities</item>
        <item quantity="other">%1$d activities</item>
    </plurals>
    <string name="training_v2_training_data_analysis_group_by_description">Select a way to group your workouts and spot patterns.</string>
    <string name="training_v2_training_data_analysis_table_show_inactive_period">Show inactive period</string>
    <string name="training_v2_this_week">This week</string>
    <string name="training_v2_this_month">This month</string>
    <string name="training_v2_this_year">This year</string>

    <string name="training_graph_type_duration">Duration</string>
    <string name="training_graph_type_distance">Distance</string>
    <string name="training_graph_type_tss">Training load</string>
    <string name="training_graph_type_calories">Calories</string>
    <string name="training_graph_type_ascent">Ascent</string>
    <string name="training_graph_type_vo2max">VO₂max</string>
    <string name="training_graph_type_avg_peed">Avg speed</string>
    <string name="training_graph_type_avg_pace">Avg pace</string>
    <string name="training_graph_type_avg_power">Avg power</string>
    <string name="training_graph_type_normalized_power">NP®</string>
    <string name="training_graph_type_avg_swim_pace">Avg swim pace</string>
    <string name="training_graph_type_avg_heart_rate">Avg Heart rate</string>
    <string name="training_graph_avg_daily">Daily avg</string>
    <string name="training_graph_avg_weekly">Weekly avg</string>
    <string name="training_graph_avg_monthly">Monthly avg</string>
    <string name="training_graph_avg_yearly">Yearly avg</string>

    <string name="training_tab_title">Training</string>
    <!-- End Training V2 -->

    <!-- Statistics -->
    <string name="statistics_tab_title">Statistics</string>
    <string name="statistics_filter_tags_clear_all">Clear all</string>
    <string name="statistics_filters">Filters</string>
    <string name="statistics_table_settings">Table settings</string>
    <!-- End Statistics -->

    <!-- Summary table label -->
    <string name="training_zone_summary_edit_columns">Edit columns</string>
    <string name="training_zone_summary_edit_columns_description">Move columns by pressing, holding, and dragging to change their position. Use toggle to hide or show the column in Summary table.</string>
    <string name="training_zone_summary_table_date">Date</string>
    <string name="training_zone_summary_table_activities">Activities</string>
    <string name="training_zone_summary_table_duration">Duration</string>
    <string name="training_zone_summary_table_distance">Distance</string>
    <string name="training_zone_summary_table_avg_speed">Speed (AVG)</string>
    <string name="training_zone_summary_table_avg_pace">Pace (AVG)</string>
    <string name="training_zone_summary_table_avg_heart_rate">Heart rate (AVG)</string>
    <string name="training_zone_summary_table_ascent">Ascent</string>
    <string name="training_zone_summary_table_tss">TSS</string>
    <string name="training_zone_summary_table_energy">Calories</string>
    <string name="training_zone_summary_table_vo2max">VO₂max</string>
    <string name="training_zone_summary_table_avg_power">Power (AVG)</string>
    <string name="training_zone_summary_table_normalized_power">NP®</string>
    <string name="training_zone_summary_table_avg_swim_pace">Swim pace (AVG)</string>
    <!-- END Summary table label -->
</resources>
