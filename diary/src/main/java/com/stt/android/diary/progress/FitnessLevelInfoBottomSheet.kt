package com.stt.android.diary.progress

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.home.diary.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun FitnessLevelInfoBottomSheet(
    onInfoProgressionClicked: () -> Unit,
    onInfoValuesClicked: () -> Unit,
    onTrainingPeaksClick: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    ModalBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismiss,
        dragHandle = {
            BottomSheetDefaults.DragHandle(
                color = MaterialTheme.colorScheme.secondary.copy(alpha = 0.4f),
            )
        },
        containerColor = MaterialTheme.colorScheme.surface,
    ) {
        FitnessLevelInfoContent(
            onInfoProgressionClicked = onInfoProgressionClicked,
            onInfoValuesClicked = onInfoValuesClicked,
            onTrainingPeaksClick = onTrainingPeaksClick,
        )
    }
}

@Composable
private fun FitnessLevelInfoContent(
    onInfoProgressionClicked: () -> Unit,
    onInfoValuesClicked: () -> Unit,
    onTrainingPeaksClick: () -> Unit,
    modifier: Modifier = Modifier,
) = Column(
    modifier = modifier
        .verticalScroll(rememberScrollState())
        .fillMaxWidth(),
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.small,
            ),
    ) {
        Text(
            text = stringResource(R.string.graph_type_fitness_level),
            style = MaterialTheme.typography.bodyXLargeBold,
            color = MaterialTheme.colorScheme.onSurface,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        CompositionLocalProvider(
            LocalTextStyle provides MaterialTheme.typography.bodyLarge,
            LocalContentColor provides MaterialTheme.colorScheme.onSurface,
        ) {
            Text(text = stringResource(R.string.progress_explanation_tss))
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            Text(text = stringResource(R.string.progress_explanation_ctl))
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            Text(text = stringResource(R.string.progress_explanation_atl))
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            Text(text = stringResource(R.string.progress_explanation_tsb))
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            Text(text = stringResource(R.string.progress_explanation_tsb_ranges))
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            Text(text = stringResource(R.string.progress_explanation_summary))
        }
    }
    TitleSummaryItem(
        title = stringResource(R.string.tss_read_more_about_training_progression_title),
        summary = stringResource(R.string.tss_read_more_about_training_progression_description),
        onClick = onInfoProgressionClicked,
    )
    HorizontalDivider(
        modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
        color = MaterialTheme.colorScheme.dividerColor,
    )
    TitleSummaryItem(
        title = stringResource(R.string.tss_read_more_about_tss_title),
        summary = stringResource(R.string.tss_read_more_about_tss_values_description),
        onClick = onInfoValuesClicked,
    )
    HorizontalDivider(
        modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
        color = MaterialTheme.colorScheme.dividerColor,
    )
    Column(
        modifier = Modifier
            .clickable(onClick = onTrainingPeaksClick)
            .fillMaxWidth()
            .padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium,
                top = MaterialTheme.spacing.xlarge,
                bottom = MaterialTheme.spacing.xxlarge,
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        Image(
            painter = painterResource(R.drawable.trainingpeaks_logo),
            contentDescription = null,
        )
        Text(
            text = stringResource(R.string.tss_training_peaks_algorithm_notice),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colorScheme.secondary,
        )
    }
}

@Composable
private fun TitleSummaryItem(
    title: String,
    summary: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) = Column(
    modifier = modifier
        .clickable(onClick = onClick)
        .fillMaxWidth()
        .padding(all = MaterialTheme.spacing.medium),
    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
) {
    Text(
        text = title,
        style = MaterialTheme.typography.bodyLarge,
        color = MaterialTheme.colorScheme.onSurface,
    )
    Text(
        text = summary,
        style = MaterialTheme.typography.body,
        color = MaterialTheme.colorScheme.secondary,
    )
}
