package com.stt.android.home

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.android.DeviceFeatureStates
import com.stt.android.domain.session.FetchSessionStatusUseCase
import com.stt.android.domain.session.ResetPasswordUseCase
import com.stt.android.domain.session.SaveAndGetSessionStatusUseCase
import com.stt.android.domain.session.SessionStatus
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.tag.TagConstants
import com.stt.android.home.BaseHomeViewModel.Companion.SESSION_STATUS_POLLING_MILLIS_AFTER_PASSWORD_RESET
import com.stt.android.home.BaseHomeViewModel.Companion.SESSION_STATUS_POLLING_MIN_INTERVAL
import com.stt.android.refreshable.Refreshables
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_PASSWORD_RESET_OR_DELETE_REQUESTED_AT
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_POST_NOTIFICATION_HAS_ASKED
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.Clock
import java.time.Instant

abstract class BaseHomeViewModel(
    private val fetchSessionStatusUseCase: FetchSessionStatusUseCase,
    private val resetPasswordUseCase: ResetPasswordUseCase,
    private val saveAndGetSessionStatusUseCase: SaveAndGetSessionStatusUseCase,
    private val sharedPreferences: SharedPreferences,
    private val clock: Clock,
    private val refreshables: Refreshables,
    private val workoutHeaderController: WorkoutHeaderController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val featureStates: DeviceFeatureStates
) : CoroutineViewModel(coroutinesDispatchers) {

    private val _smsVerificationNeeded = SingleLiveEvent<Any>()
    private var refreshJob: Job? = null

    private val _enforceLogout: MutableLiveData<Boolean> =
        MutableLiveData<Boolean>().apply { value = false }

    val enforceLogout: LiveData<Boolean>
        get() = _enforceLogout

    val smsVerificationNeeded: LiveData<Any>
        get() = _smsVerificationNeeded

    private var sessionStatusPolledAt = 0L

    private val _showTrackingCO2EmissionsReducedTooltip = SingleLiveEvent<Unit>()
    val showTrackingCO2EmissionsReducedTooltip: LiveData<Unit>
        get() = _showTrackingCO2EmissionsReducedTooltip

    private val _showAutoTaggedDialog = SingleLiveEvent<WorkoutHeader>()
    val showAutoTaggedDialog: LiveData<WorkoutHeader>
        get() = _showAutoTaggedDialog

    val areNotificationsEnabled get() = featureStates.areNotificationsEnabled()

    private val _navigationItemSelected = SingleLiveEvent<Int>()
    private val _navigationItemReSelected = SingleLiveEvent<Int>()

    val navigationItemSelected: LiveData<Int>
        get() = _navigationItemSelected

    val navigationItemReSelected: LiveData<Int>
        get() = _navigationItemReSelected

    init {
        pollAccountStatus()
    }

    /**
     * Conditionally poll account status and enforce logout. A poll is performed if password reset was requested
     * less than [SESSION_STATUS_POLLING_MILLIS_AFTER_PASSWORD_RESET] milliseconds ago and there has not been a poll
     * request within the last [SESSION_STATUS_POLLING_MIN_INTERVAL] milliseconds.
     *
     * An initial poll is performed once even if password reset has never been requested.
     */
    fun pollAccountStatusIfNeeded() {
        sharedPreferences.getLong(KEY_PASSWORD_RESET_OR_DELETE_REQUESTED_AT, -1L).takeIf { it > 0 }
            ?.let { resetAt ->
                val now = Instant.now(clock).toEpochMilli()

                if (resetAt + SESSION_STATUS_POLLING_MILLIS_AFTER_PASSWORD_RESET > now &&
                    sessionStatusPolledAt + SESSION_STATUS_POLLING_MIN_INTERVAL < now
                ) {
                    pollAccountStatus()
                }
            }
    }

    abstract fun setMapStyleFromDeepLink(
        style: String?,
        enable3D: Boolean?,
        roadSurfaces: List<String>?,
        hideCyclingForbidden: Boolean?
    )

    private fun resetPasswordResetOrDeleteRequestTime() {
        Timber.d("resetPasswordResetOrDeleteRequestTime")
        sharedPreferences.edit {
            remove(KEY_PASSWORD_RESET_OR_DELETE_REQUESTED_AT)
        }
    }

    private fun pollAccountStatus() {
        Timber.d("pollAccountStatus: doing poll")
        sessionStatusPolledAt = Instant.now(clock).toEpochMilli()

        launch {
            runSuspendCatching {
                val sessionStatus = fetchSessionStatusUseCase()
                saveAndGetSessionStatusUseCase.saveSessionStatus(sessionStatus)

                if (sessionStatus == SessionStatus.INVALID_PWD_RESET) {
                    resetPasswordUseCase.resetPassword()
                }

                when (sessionStatus) {
                    SessionStatus.VALID -> {
                        // session is valid, do nothing
                        Timber.d("Session status is valid")
                        _enforceLogout.value = false
                    }

                    SessionStatus.INVALID_NEED_LOGIN,
                    SessionStatus.INVALID_PWD_RESET,
                    SessionStatus.INVALID_ACCOUNT_INCOMPLETE -> {
                        // invalid session, logout
                        Timber.w("Session status is $sessionStatus, logging out")
                        resetPasswordResetOrDeleteRequestTime()
                        _enforceLogout.value = true
                    }

                    SessionStatus.UNKNOWN -> {
                        // unknown session, do nothing
                        Timber.w("Session status is UNKNOWN")
                        _enforceLogout.value = false
                    }

                    SessionStatus.SMS_VERIFICATION_NEEDED -> {
                        _smsVerificationNeeded.call()
                    }
                }
            }.onFailure { e ->
                Timber.w(e, "Error while fetching session status")
            }
        }
    }

    fun refreshAppData(force: Boolean) {
        val job = refreshJob
        if (job == null || !job.isActive) {
            Timber.d("Refreshing app data")
            refreshJob = launch {
                refreshables.refresh(skipRateLimiting = force)
            }
        }
    }

    fun showAutoTaggedDialogIfNeeded() {
        if (_showAutoTaggedDialog.value != null) {
            // Dialog is visible to the user now, don't notify live data again
            // This may happen due to screen rotation (this method is called from onResume)
            return
        }

        val hasShownAutoTaggedDialog =
            sharedPreferences.getBoolean(TagConstants.HAS_SHOWN_AUTO_TAGGED_DIALOG, false)
        if (!hasShownAutoTaggedDialog) {
            val autoTaggedWorkoutKey =
                sharedPreferences.getString(TagConstants.AUTO_TAGGED_WORKOUT_KEY, null)

            if (autoTaggedWorkoutKey != null) {
                viewModelScope.launch(coroutinesDispatchers.io) {
                    runSuspendCatching {
                        workoutHeaderController
                            .find(autoTaggedWorkoutKey)
                            ?.let {
                                _showAutoTaggedDialog.postValue(it)
                            }
                    }.onFailure {
                        Timber.w(it)
                    }
                }
            }
        }
    }

    fun onAutoTaggedDialogDismissed() {
        // Consider auto tagged dialog shown only when the user close it
        sharedPreferences.edit {
            putBoolean(TagConstants.HAS_SHOWN_AUTO_TAGGED_DIALOG, true)
            remove(TagConstants.AUTO_TAGGED_WORKOUT_KEY)
        }

        _showTrackingCO2EmissionsReducedTooltip.postValue(Unit)
    }

    fun isPostNotificationAsked() =
        sharedPreferences.getBoolean(KEY_POST_NOTIFICATION_HAS_ASKED, false)

    fun setPostNotificationAsked() = sharedPreferences.edit {
        putBoolean(KEY_POST_NOTIFICATION_HAS_ASKED, true)
    }

    override fun onCleared() {
        super.onCleared()
        refreshJob?.cancel()
        refreshJob = null
    }

    fun setNavigationItemSelected(itemId: Int) {
        _navigationItemSelected.postValue(itemId)
    }

    fun setNavigationItemReselected(itemId: Int) {
        _navigationItemReSelected.postValue(itemId)
    }

    companion object {
        /**
         * After requesting a password change, session status is polled on HomeActivity.onStart() for this many
         * milliseconds or until logging out.
         */
        private const val SESSION_STATUS_POLLING_MILLIS_AFTER_PASSWORD_RESET =
            24 * 60 * 60 * 1000L // 24 hours

        /**
         * Do not poll session status faster than once in this many milliseconds even if user is actively using the app.
         */
        private const val SESSION_STATUS_POLLING_MIN_INTERVAL = 60 * 1000L // 60 seconds
    }
}
