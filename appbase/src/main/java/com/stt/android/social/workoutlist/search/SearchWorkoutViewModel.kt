package com.stt.android.social.workoutlist.search

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.eventtracking.EventTracker
import com.stt.android.social.workoutlist.DateHeader
import com.stt.android.social.workoutlist.search.SearchWorkoutActivity.Companion.KEY_USERNAME
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.format.DateTimeFormatter
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class SearchWorkoutViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    savedStateHandle: SavedStateHandle,
    currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val eventTracker: EventTracker,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {

    private val _searchQuery = MutableStateFlow("")

    private val _viewState = MutableStateFlow(
        SearchWorkoutViewState("", emptyList())
    )
    val viewState: StateFlow<SearchWorkoutViewState> = _viewState.asStateFlow()

    private val userName =
        savedStateHandle.get<String>(KEY_USERNAME) ?: currentUserController.username
    private val trackPageName = savedStateHandle.get<String>(AnalyticsEventProperty.PAGE_NAME) ?: ""

    init {
        viewModelScope.launch {
            _searchQuery.collect { query ->
                updateViewState(query)
            }
        }
    }

    private suspend fun updateViewState(query: String) {
        withContext(coroutinesDispatchers.io) {
            if (query.isNotBlank()) {
                eventTracker.trackEvent(
                    AnalyticsEvent.SEARCH_REQUEST, mapOf(
                        AnalyticsEventProperty.PAGE_NAME to trackPageName,
                        AnalyticsEventProperty.SEARCH_WORD to query,
                    )
                )
            }
            val allWorkouts = workoutHeaderController.findAllWhereOwner(userName, false)
                .takeUnless { it.isEmpty() }
                ?.filter {
                    if (query.isBlank()) {
                        false
                    } else {
                        val searchTerms = query.lowercase().split(" ").toTypedArray()
                        it.applyFilter(searchTerms, context.resources)
                    }
                }
                ?.sortedByDescending(WorkoutHeader::startTime)
                ?: emptyList()

            val dateFormatter = DateTimeFormatter.ofPattern(
                "MMMM yyyy",
                Locale(context.getString(com.stt.android.R.string.language_code)),
            )

            val allWorkoutsWithDates = allWorkouts.groupBy {
                dateFormatter.format(it.startTime.toLocalDate())
            }.flatMap { (date, workouts) ->
                listOf(DateHeader(date, workouts.size)) + workouts
            }

            _viewState.value = SearchWorkoutViewState(query, allWorkoutsWithDates)
        }
    }

    fun onQueryChange(query: String) {
        _searchQuery.value = query
    }
}
