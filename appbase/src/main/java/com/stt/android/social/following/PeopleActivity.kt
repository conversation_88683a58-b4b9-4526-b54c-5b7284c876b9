package com.stt.android.social.following

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.eventtracking.EventTracker
import com.stt.android.home.KEY_SHOW_FOLLOWING_TAB
import com.stt.android.home.KEY_SHOW_PENDING_REQUESTS
import com.stt.android.home.people.PeopleFragment
import com.stt.android.social.friends.FriendsFragment
import com.stt.android.social.friends.FriendsViewModel
import com.stt.android.social.friends.search.SearchFriendsActivity
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class PeopleActivity : AppCompatActivity() {
    private val viewModel: FriendsViewModel by viewModels()

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    @Inject
    lateinit var eventTracker: EventTracker

    private val newFriendsEnabled: Boolean
        get() = featureTogglePreferences.getBoolean(
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_FRIENDS,
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_FRIENDS_DEFAULT
        )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_people)

        setupBackPressedCallback()

        if (newFriendsEnabled) {
            openFriendsFragment()
            setupActionBarForFriendsFragment()
        } else {
            openPeopleFragment()
            setupActionBar()
        }
    }

    private fun setupBackPressedCallback() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (viewModel.editing) {
                    viewModel.setEditingMode(false)
                } else {
                    isEnabled = false
                    onBackPressedDispatcher.onBackPressed()
                }
            }
        })
    }

    @SuppressLint("CommitTransaction")
    private fun openPeopleFragment() {
        var peopleFragment = supportFragmentManager.findFragmentByTag(PeopleFragment.FRAGMENT_TAG)
        if (peopleFragment == null) {
            val showPendingRequests =
                intent.extras?.getBoolean(KEY_SHOW_PENDING_REQUESTS, false) ?: false
            val showFollowingTab =
                intent.extras?.getBoolean(KEY_SHOW_FOLLOWING_TAB, false) ?: false
            peopleFragment = PeopleFragment.newInstance(showPendingRequests, showFollowingTab)
            supportFragmentManager.beginTransaction()
                .replace(
                    R.id.people_fragment_container,
                    peopleFragment,
                    PeopleFragment.FRAGMENT_TAG
                ).commit()
        }
    }

    private fun setupActionBar() {
        setSupportActionBar(findViewById(R.id.toolbar))
        supportActionBar?.apply {
            setDisplayShowHomeEnabled(false)
            setDisplayHomeAsUpEnabled(true)
            title = resources.getString(R.string.followers)
        }
    }

    @SuppressLint("CommitTransaction")
    private fun openFriendsFragment() {
        var friendsFragment = supportFragmentManager.findFragmentByTag(FriendsFragment.FRAGMENT_TAG)
        if (friendsFragment == null) {
            friendsFragment = FriendsFragment.newInstance()
            supportFragmentManager.beginTransaction()
                .replace(
                    R.id.people_fragment_container,
                    friendsFragment,
                    PeopleFragment.FRAGMENT_TAG
                ).commit()
        }
    }

    private fun setupActionBarForFriendsFragment() {
        setSupportActionBar(findViewById(R.id.toolbar))
        supportActionBar?.apply {
            setDisplayShowHomeEnabled(false)
            setDisplayHomeAsUpEnabled(true)
            title = resources.getString(R.string.friends)
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        if (viewModel.editing) {
            viewModel.setEditingMode(false)
            return true
        }
        onBackPressedDispatcher.onBackPressed()
        return true
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        if (newFriendsEnabled) {
            menuInflater.inflate(R.menu.menu_friends, menu)
            observeEditingMode(menu)
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_search_friends -> {
                eventTracker.trackEvent(
                    AnalyticsEvent.SEARCH_CLICK,
                    mapOf(AnalyticsEventProperty.PAGE_NAME to TRACK_PAGE_NAME)
                )
                startActivity(SearchFriendsActivity.newStartIntent(this, TRACK_PAGE_NAME))
                true
            }

            R.id.action_delete_friends -> {
                viewModel.revokeSelectedFollowers()
                true
            }

            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun updateMenuVisibility(menu: Menu, editing: Boolean, selectedCount: Int) {
        val searchItem = menu.findItem(R.id.action_search_friends)
        val deleteItem = menu.findItem(R.id.action_delete_friends)

        searchItem?.isVisible = !editing
        deleteItem?.isVisible = editing

        val enableDelete = selectedCount > 0
        deleteItem?.isEnabled = enableDelete
        deleteItem?.icon?.alpha = if (enableDelete) 0xFF else 0x9E
    }

    private fun observeEditingMode(menu: Menu) {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.followersStateFlow.collect { followersState ->
                    val editing = followersState.editing
                    val selectedCount = followersState.selectedFriends.size
                    updateNavigationIcon(editing)
                    updateTitle(editing)
                    invalidateOptionsMenu()
                    updateMenuVisibility(menu, viewModel.editing, selectedCount)
                }
            }
        }
    }

    private fun updateNavigationIcon(editing: Boolean) {
        supportActionBar?.run {
            if (editing) {
                setHomeAsUpIndicator(SuuntoIcons.ActionClose.resource)
            } else {
                setHomeAsUpIndicator(null)
            }
        }
    }

    private fun updateTitle(editing: Boolean) {
        if (editing) {
            updateSelectedCountTitle(viewModel.selectedFriendsCount.value)
        } else {
            supportActionBar?.title = resources.getString(R.string.friends)
        }
    }

    private fun updateSelectedCountTitle(count: Int) {
        supportActionBar?.title = resources.getString(R.string.selected_item_count, count)
    }

    companion object {
        private const val TRACK_PAGE_NAME = "Friends"

        @JvmStatic
        @JvmOverloads
        fun newIntent(
            context: Context,
            showPendingRequests: Boolean = false,
            showFollowingTab: Boolean = false
        ) = Intent(context, PeopleActivity::class.java).apply {
            putExtra(KEY_SHOW_PENDING_REQUESTS, showPendingRequests)
            putExtra(KEY_SHOW_FOLLOWING_TAB, showFollowingTab)
        }
    }
}
