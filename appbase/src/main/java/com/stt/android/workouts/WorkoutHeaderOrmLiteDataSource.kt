package com.stt.android.workouts

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.utils.STTConstants
import com.suunto.algorithms.data.Length
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

// TODO remove this class once workouts are migrated to Room TP #87987
class WorkoutHeaderOrmLiteDataSource
@Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
    private val localBroadcastManager: LocalBroadcastManager,
    private val deleteWorkoutUseCase: DeleteWorkoutUseCase,
    private val currentUserController: CurrentUserController
) : WorkoutHeaderDataSource {
    private val currentUsername
        get() = currentUserController.currentUser.username

    override suspend fun remove(key: String) = withContext<Unit>(ORMLITE) {
        workoutHeaderController.find(key)
            ?.let(workoutHeaderController::remove)
    }

    override suspend fun getDeletedWorkoutsKeys(): List<String> = withContext(ORMLITE) {
        workoutHeaderController.findAllDeleted(currentUsername)
            .mapNotNull { it.key }
    }

    override suspend fun storeWorkout(workoutHeader: WorkoutHeader) {
        withContext(ORMLITE) {
            workoutHeaderController.store(workoutHeader)
            val intent = Intent(STTConstants.BroadcastActions.WORKOUT_SYNCED)
            intent.putExtra(STTConstants.ExtraKeys.WORKOUT_OLD_ID, workoutHeader.id)
            intent.putExtra(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader)
            localBroadcastManager.sendBroadcast(intent)
        }
    }

    override suspend fun markDeletedOrPermanentlyDelete(id: Int): Boolean = withContext(ORMLITE) {
        val workoutHeader = workoutHeaderController.findByIdsForCurrentUser(listOf(id)).first()

        val deleted = deleteWorkoutUseCase(workoutHeader)

        localBroadcastManager.sendBroadcast(
            Intent(STTConstants.BroadcastActions.WORKOUT_DELETED)
                .putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutHeader.id)
        )

        deleted
    }

    override suspend fun markExtensionsFetched(id: Int): Boolean = withContext(ORMLITE) {
        workoutHeaderController.markExtensionsFetched(id)
    }

    override suspend fun getLocallyModifiedWorkouts(): List<WorkoutHeader> = withContext(ORMLITE) {
        workoutHeaderController.findSyncedButLocallyChanged(currentUsername)
    }

    override suspend fun markWorkoutsAsSynced(workoutKeys: Set<String>) = withContext(ORMLITE) {
        workoutHeaderController.markAsSynced(workoutKeys.toList())
    }

    override suspend fun findManuallyCreatedWorkouts(): List<WorkoutHeader> = withContext(ORMLITE) {
        workoutHeaderController.findManuallyCreatedWorkouts(currentUsername)
    }

    override suspend fun findNewUnsyncedWorkouts(): List<WorkoutHeader> = withContext(ORMLITE) {
        workoutHeaderController.findNeverSyncedWorkoutsForUser(currentUsername)
    }

    override fun syncWorkouts() {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun findByKey(key: String): WorkoutHeader? = withContext(ORMLITE) {
        try {
            workoutHeaderController.find(key)
        } catch (e: Exception) {
            Timber.w(e, "An error has occurred while trying to find workout by key")
            null
        }
    }

    override suspend fun findById(id: Int): WorkoutHeader? = withContext(ORMLITE) {
        try {
            workoutHeaderController.find(listOf(id)).toBlocking().first()
                ?.firstOrNull()
        } catch (e: Exception) {
            Timber.w(e, "An error has occurred while trying to find workout by id")
            null
        }
    }

    override suspend fun findByIds(ids: List<Int>): List<WorkoutHeader> =
        withContext(ORMLITE) {
            try {
                workoutHeaderController.find(ids).toBlocking().first() ?: emptyList()
            } catch (e: Exception) {
                Timber.w(e, "An error occurred while trying to find workouts by ids")
                emptyList()
            }
        }

    override suspend fun findPagedOfType(
        ownerUsername: String,
        activityTypeId: Int,
        page: Int
    ): List<WorkoutHeader> = try {
        workoutHeaderController.findAllPagedOfType(
            ownerUsername,
            false,
            activityTypeId,
            page
        )
    } catch (e: Exception) {
        Timber.w(e, "An error occurred while trying to find workouts by page")
        emptyList()
    }

    override suspend fun findPagedExcludingTypes(
        ownerUsername: String,
        excludedTypes: Set<Int>,
        page: Int
    ): List<WorkoutHeader> = try {
        workoutHeaderController.findAllPagedExcludingTypes(
            ownerUsername,
            false,
            excludedTypes,
            page
        )
    } catch (e: Exception) {
        Timber.w(e, "An error occurred while trying to find workouts by page")
        emptyList()
    }

    override suspend fun findByStartTime(
        ownerUsername: String,
        minimumStartTime: Long,
        maximumStartTime: Long
    ): List<WorkoutHeader> {
        return workoutHeaderController.findByUserAndStartTime(
            ownerUsername,
            minimumStartTime,
            maximumStartTime
        )
    }

    override suspend fun findNotDeletedByRange(
        ownerUsername: String,
        activityTypeId: Int?,
        sinceMs: Long,
        untilMs: Long
    ): List<WorkoutHeader> = try {
        workoutHeaderController.findWorkoutHeaders(
            ownerUsername,
            activityTypeId,
            sinceMs,
            untilMs
        )
    } catch (e: Exception) {
        Timber.w(e, "An error occurred while trying to find workouts by range")
        emptyList()
    }

    override suspend fun findPagedByTimeRange(
        ownerUsername: String,
        sinceMs: Long,
        untilMs: Long,
        includeActivityTypeId: Int?,
        excludeActivityTypeIds: Set<Int>,
        page: Int,
        firstPageSize: Int,
        pageSize: Int
    ): List<WorkoutHeader> = try {
        workoutHeaderController.findPagedByTimeRange(
            ownerUsername,
            sinceMs,
            untilMs,
            includeActivityTypeId,
            excludeActivityTypeIds,
            page,
            firstPageSize,
            pageSize
        )
    } catch (e: Exception) {
        Timber.w(e, "An error occurred while trying to find paged workouts by time range")
        emptyList()
    }

    override suspend fun loadActivityTypeCount(id: Int): Long {
        return workoutHeaderController.loadActivityTypeCount(id)
    }

    override suspend fun loadTotalActivityCount(): Long {
        return workoutHeaderController.syncedWorkoutsCount(currentUsername)
    }

    override suspend fun loadActivityCountInPeriod(since: Long, till: Long): Long {
        return workoutHeaderController.loadActivityCountInPeriod(since, till)
    }

    override suspend fun loadActivityTypeCountInPeriod(id: Int, since: Long, till: Long): Long {
        return workoutHeaderController.loadActivityTypeCountInPeriod(id, since, till)
    }

    override suspend fun loadFastestOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderController.loadFastestOfActivityTypeInPeriod(id, since, till)
    }

    override suspend fun loadFarthestOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderController.loadFarthestOfActivityTypeInPeriod(id, since, till)
    }

    override suspend fun loadShortestTimeOfActivityTypeWithDistanceRange(
        id: Int,
        distanceRange: ClosedRange<Length>,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderController.loadShortestTimeOfActivityTypeInPeriodWithinDistance(
            id,
            since,
            till,
            distanceRange.start.inMeters,
            distanceRange.endInclusive.inMeters
        )
    }

    override suspend fun loadLatestOfActivityType(id: Int, till: Long): WorkoutHeader? {
        return workoutHeaderController.loadLatestOfActivityType(id, till)
    }

    override suspend fun loadLongestTimeOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderController.loadLongestOfActivityTypeInPeriod(id, since, till)
    }

    override suspend fun loadHighestClimbOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderController.loadHighestClimbOfActivityTypeInPeriod(id, since, till)
    }

    override suspend fun loadHighestAltitudeOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderController.loadHighestAltitudeOfActivityTypeInPeriod(id, since, till)
    }

    override suspend fun loadFastestPaceOfActivityTypeInPeriod(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderController.loadFastestPaceOfActivityTypeInPeriod(id, since, till)
    }

    override suspend fun findWithUserTagsById(id: Int): WorkoutHeader? =
        withContext(ORMLITE) {
            try {
                workoutHeaderController.findNotDeletedWithUserTags(id)
            } catch (e: Exception) {
                Timber.w(e, "An error has occurred while trying to find workout by id")
                null
            }
        }

    override suspend fun findWorkoutsWithUnsyncedUserTags(): List<WorkoutHeader> =
        withContext(ORMLITE) {
            try {
                workoutHeaderController.findWorkoutsWithUnsyncedUserTags()
            } catch (e: Exception) {
                Timber.w(e, "An error occurred while trying to find workouts workouts with unsynced user tags")
                emptyList()
            }
        }

    override suspend fun findOldestWorkout(ownerUsername: String, since: Long): WorkoutHeader? =
        withContext(ORMLITE) {
            workoutHeaderController.findOldestNotDeletedWorkout(ownerUsername, since)
        }
}
