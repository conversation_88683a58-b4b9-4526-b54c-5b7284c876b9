package com.stt.android.ui.activities.competition

import android.content.Context
import androidx.annotation.DrawableRes
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.ViewState
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.WorkoutHeaderController.SimilarWorkoutsDistanceThreshold
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.advancedlaps.LapsTable
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.sml.FetchSmlUseCase
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionStatus
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummary
import com.stt.android.home.diary.diarycalendar.activitygroups.ActivityTypeToGroupMapper
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.controllers.WorkoutDataLoaderController
import com.stt.android.ui.controllers.loadWorkout
import com.stt.android.ui.extensions.isSimilarRouteWorkout
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.workoutcomparison.GhostTargetForNotSimilarRoute
import com.stt.android.workoutcomparison.WorkoutComparisonUiState
import com.stt.android.workoutcomparison.WorkoutComparisonViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class WorkoutCompetitionViewModel @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val savedStateHandle: SavedStateHandle,
    private val activityTypeToGroupMapper: ActivityTypeToGroupMapper,
    private val workoutDataLoaderController: WorkoutDataLoaderController,
    mapSelectionModel: MapSelectionModel,
    private val userSettingsController: UserSettingsController,
    private val fetchSmlUseCase: FetchSmlUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val infoModelFormatter: InfoModelFormatter,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val detailedComparisonLoader: DetailedComparisonLoader,
    private val lapsDataProvider: LapsDataProvider,
    private val workoutHeaderController: WorkoutHeaderController
) : WorkoutComparisonViewModel(
    appContext = appContext,
    savedStateHandle = savedStateHandle,
    activityTypeToGroupMapper = activityTypeToGroupMapper,
    workoutDataLoaderController = workoutDataLoaderController,
    mapSelectionModel = mapSelectionModel,
    userSettingsController = userSettingsController,
    fetchSmlUseCase = fetchSmlUseCase,
    coroutinesDispatchers = coroutinesDispatchers,
) {
    private val _competitionUIState: MutableStateFlow<WorkoutCompetitionUIState> =
        MutableStateFlow(WorkoutCompetitionUIState.Loading)
    val competitionUIState: StateFlow<WorkoutCompetitionUIState> = _competitionUIState.asStateFlow()
    private val _filterUIState: MutableStateFlow<LapsComparisonFilter> =
        MutableStateFlow(LapsComparisonFilter())
    val filterUiState = _filterUIState.asStateFlow()

    @Volatile
    private lateinit var otherWorkoutHeader: WorkoutHeader

    private var currentWorkoutSml: Sml? = null
    private var otherWorkoutSml: Sml? = null

    override val loadOnInit: Boolean = true

    override fun getCurrentWorkoutHeader(): WorkoutHeader = savedStateHandle.currentWorkoutHeader

    override fun getOtherWorkoutHeader(): WorkoutHeader = otherWorkoutHeader

    init {
        viewModelScope.launch {
            if (checkIfNeededToLoadOtherWorkout()) {
                loadOtherWorkoutCheckIfSimilar()
                loadDetailedComparisonData()
                loadLapsComparisonDataBar()
                loadLapsComparisonData()
            }
        }
    }


    private fun checkIfNeededToLoadOtherWorkout(): Boolean =
        when (savedStateHandle.workoutResult.status) {
            CompetitionStatus.OK.name -> true
            CompetitionStatus.DELETED.name -> {
                _competitionUIState.update {
                    WorkoutCompetitionUIState.Error(
                        CompetitionErrorEnum.DELETED_BY_USER,
                        getCurrentWorkoutHeader().activityType.iconId,
                        activityTypeToGroupMapper.activityTypeIdToGroup(
                            getCurrentWorkoutHeader().activityType.id
                        )
                    )
                }
                false
            }

            CompetitionStatus.FORBIDDEN.name -> {
                _competitionUIState.update {
                    WorkoutCompetitionUIState.Error(
                        CompetitionErrorEnum.FORBIDDEN_BY_USER,
                        getCurrentWorkoutHeader().activityType.iconId,
                        activityTypeToGroupMapper.activityTypeIdToGroup(
                            getCurrentWorkoutHeader().activityType.id
                        )
                    )
                }
                false
            }
            else -> false
        }

    private suspend fun loadOtherWorkoutCheckIfSimilar() {
        _competitionUIState.update { WorkoutCompetitionUIState.Loading }
        _competitionUIState.update {
            runSuspendCatching {
                otherWorkoutHeader = requireNotNull(loadOtherWorkout())
                if (areWorkoutsSimilar()) {
                    handleSimilarWorkoutComparison()
                } else {
                    handleNonSimilarWorkoutComparison()
                }
            }.getOrElse { e ->
                Timber.w(e, "Failed to load other workout for competition")
                WorkoutCompetitionUIState.Error(
                    CompetitionErrorEnum.LOAD_OTHER_WORKOUT_FAILED,
                    getCurrentWorkoutHeader().activityType.iconId,
                    activityTypeToGroupMapper.activityTypeIdToGroup(
                        getCurrentWorkoutHeader().activityType.id
                    )
                )
            }
        }
    }

    private fun areWorkoutsSimilar(): Boolean {
        val threshold =
            SimilarWorkoutsDistanceThreshold.getByDistance(otherWorkoutHeader.totalDistance)
        return getCurrentWorkoutHeader().isSimilarRouteWorkout(otherWorkoutHeader, threshold)
    }

    private suspend fun handleSimilarWorkoutComparison(): WorkoutCompetitionUIState {
        return when (val uiState = loadWorkoutComparison()) {
            is WorkoutComparisonUiState.Loaded -> buildWorkoutCompetitionLoaded(uiState)
            is WorkoutComparisonUiState.Error -> throw Exception("Comparison data is null")
            else -> WorkoutCompetitionUIState.Loading
        }
    }

    private suspend fun handleNonSimilarWorkoutComparison() =
        buildWorkoutCompetitionLoaded(loadWorkoutComparisonForNotSimilarRoute())

    private suspend fun loadWorkoutComparisonForNotSimilarRoute() =
        withContext(coroutinesDispatchers.io) {
            val currentWorkoutDataAsync = async {
                workoutDataLoaderController.loadWorkout(getCurrentWorkoutHeader())
            }
            val otherWorkoutDataAsync = async {
                workoutDataLoaderController.loadWorkout(
                    otherWorkoutHeader
                )
            }
            val currentWorkoutData = currentWorkoutDataAsync.await()
            val otherWorkoutData = otherWorkoutDataAsync.await()
            ensureSmlInitialized()
            val ghostTarget = GhostTargetForNotSimilarRoute(
                otherWorkoutData,
                currentWorkoutSml ?: throw IllegalStateException("currentWorkoutSml is null"),
                otherWorkoutSml ?: throw IllegalStateException("otherWorkoutSml is null")
            )

            val (comparisonEntries, comparisonLines) = createComparisonEntriesAndLines(
                currentWorkoutData = currentWorkoutData,
                otherWorkoutData = otherWorkoutData,
                currentWorkoutSml = currentWorkoutSml,
                otherWorkoutSml = otherWorkoutSml,
                ghostTarget = ghostTarget
            )

            WorkoutComparisonUiState.Loaded(
                activityIcon = getCurrentWorkoutHeader().activityType.iconId,
                activityGroup = activityTypeToGroupMapper.activityTypeIdToGroup(
                    getCurrentWorkoutHeader().activityType.id
                ),
                activityTime = appContext.getString(
                    R.string.compare_workout_date_template,
                    TextFormatter.formatRelativeDateSpan(
                        appContext.resources,
                        getCurrentWorkoutHeader().startTime
                    ),
                    TextFormatter.formatRelativeDateSpan(
                        appContext.resources,
                        otherWorkoutHeader.startTime
                    ),
                ),
                activityDate = TextFormatter.formatDate(
                    appContext,
                    getCurrentWorkoutHeader().startTime,
                    true
                ),
                otherActivityDate = TextFormatter.formatDate(
                    appContext,
                    otherWorkoutHeader.startTime,
                    true
                ),
                distanceUnit = appContext.resources
                    .getString(userSettingsController.settings.measurementUnit.distanceUnit),
                comparisonEntries = comparisonEntries,
                comparisonLines = comparisonLines,
                workoutComparisonMap = null,
            )
        }

    private suspend fun fetchSml(workoutHeader: WorkoutHeader) =
        fetchSmlUseCase.fetchSml(
            workoutHeader.id,
            workoutHeader.key,
        )

    private suspend fun WorkoutCompetitionViewModel.buildWorkoutCompetitionLoaded(
        uiState: WorkoutComparisonUiState.Loaded
    ): WorkoutCompetitionUIState {
        val competition = savedStateHandle.workoutResult.competition
        val result = when (competition?.result) {
            CompetitionResultEnum.WINNER.value -> true to false
            CompetitionResultEnum.LOSER.value -> false to true
            else -> false to true
        }
        return WorkoutCompetitionUIState.Loaded(
            usersProfile = buildUserProfile(
                getCurrentWorkoutHeader(),
                result.first,
                competition?.finishDuration
            ) to buildUserProfile(
                otherWorkoutHeader, result.second, competition?.targetDuration
            ),
            competitionResult = CompetitionResultEnum.entries.first { it.value == savedStateHandle.workoutResult.competition?.result },
            workoutComparisonUiState = uiState,
        )
    }

    private suspend fun loadDetailedComparisonData() {
        ensureSmlInitialized()
        _competitionUIState.update {
            (_competitionUIState.value as? WorkoutCompetitionUIState.Loaded)?.copy(
                detailedComparisonList = detailedComparisonLoader.loadDetailedComparisonData(
                    getCurrentWorkoutHeader(),
                    getOtherWorkoutHeader(),
                    currentWorkoutSml,
                    otherWorkoutSml
                )
            ) ?: it
        }
    }

    private suspend fun ensureSmlInitialized() {
        withContext(coroutinesDispatchers.io) {
            currentWorkoutSml = currentWorkoutSml ?: fetchSml(getCurrentWorkoutHeader())
            otherWorkoutSml = otherWorkoutSml ?: fetchSml(otherWorkoutHeader)
        }
    }

    private suspend fun loadLapsComparisonDataBar() {
        lapsDataProvider.loadCurrentDataAndGetDistanceFilters(getCurrentWorkoutHeader())
            ?.let { distanceFilters ->
                updateFilterState {
                    it.copy(
                        lapDistanceFilters = distanceFilters,
                        selectedLapsTable = distanceFilters.takeIf { it.isNotEmpty() }?.keys?.first(),
                        summarySelectors = SummarySelector.entries.toMutableList().apply {
                            remove(
                                if (!distanceFilters.keys.map { it.lapsType }.contains(LapsTableType.DURATION_AUTO_LAP)) {
                                    SummarySelector.SUMMARY_DISTANCE
                                } else {
                                    SummarySelector.SUMMARY_DURATION
                                }
                            )
                        }
                    )
                }
            }
    }

    private suspend fun loadLapsComparisonData() {
        _filterUIState.value.selectedLapsTable?.let {
            lapsDataProvider.loadLapsComparisonData(
                getCurrentWorkoutHeader(),
                it,
                getOtherWorkoutHeader(),
                getSummaryItemFromSelector(_filterUIState.value.selectedSummarySelector)
            )
                .onEach { result ->
                    Timber.d("Flow emitted: $result")
                    handleLapsComparisonResult(result)
                }.launchIn(viewModelScope)
        }
    }

    private suspend fun updateLapsComparison() {
        setLoadingComparisonState()
        val selectedLapsTableType = _filterUIState.value.selectedLapsTable
        val selector = _filterUIState.value.selectedSummarySelector

        selectedLapsTableType?.let {
            lapsDataProvider.compareCurrentAndOtherData(
                getCurrentWorkoutHeader(),
                it,
                getSummaryItemFromSelector(selector)
            )
                .onEach { result ->
                    handleLapsComparisonResult(result)
                }.launchIn(viewModelScope)
        }
    }

    private fun handleLapsComparisonResult(result: ViewState<List<Pair<LapsValueItem?, LapsValueItem?>>>) {
        if (result.isLoaded() && result.data != null) {
            _competitionUIState.update { state ->
                if (state is WorkoutCompetitionUIState.Loaded) {
                    state.copy(lapsComparisonData = LapsComparisonData(result.data, false))
                } else {
                    state
                }
            }
        }
    }

    private fun getSummaryItemFromSelector(selector: SummarySelector): SummaryItem = when (selector) {
        SummarySelector.SUMMARY_DISTANCE -> SummaryItem.DISTANCE
        SummarySelector.SUMMARY_DURATION -> SummaryItem.DURATION
        SummarySelector.SUMMARY_PACE -> SummaryItem.AVGPACE
        SummarySelector.SUMMARY_HEART_RATE -> SummaryItem.AVGHEARTRATE
    }

    private suspend fun buildUserProfile(workoutHeader: WorkoutHeader, isWinner: Boolean, duration: Long?) =
        UserProfile(
            duration = duration?.let {
                infoModelFormatter.formatValueAsString(
                    SummaryItem.DURATION,
                    it
                )
            } ?: "",
            displayedName = loadUser(workoutHeader)?.realNameOrUsername,
            avatarUrl = loadUser(workoutHeader)?.profileImageUrl,
            isWinner = isWinner
        )

    private suspend fun loadUser(workoutHeader: WorkoutHeader): User? = runSuspendCatching {
        getUserByUsernameUseCase.getUserByUsername(
            username = workoutHeader.username,
            queryRemoteIfNeeded = true,
        )
    }.getOrElse { e ->
        Timber.w(e, "Failed to load user")
        null
    }

    private suspend fun loadOtherWorkout(): WorkoutHeader? =
        withContext(coroutinesDispatchers.io) {
            try {
                workoutHeaderController.getOrFetch(requireNotNull(savedStateHandle.workoutResult.competition?.workoutId))
            } catch (e: Exception) {
                Timber.w(e, "Error while loading workout")
                null
            }
        }

    fun distanceSelectorClicked(lapsTable: LapsTable) = updateFilter(lapsTable = lapsTable)

    fun summarySelectorClicked(selector: SummarySelector) = updateFilter(selector = selector)

    private fun updateFilter(lapsTable: LapsTable? = null, selector: SummarySelector? = null) {
        updateFilterState {
            it.copy(
                selectedLapsTable = lapsTable ?: it.selectedLapsTable,
                selectedSummarySelector = selector ?: it.selectedSummarySelector
            )
        }
        viewModelScope.launch {
            updateLapsComparison()
        }
    }

    private fun updateFilterState(update: (LapsComparisonFilter) -> LapsComparisonFilter) {
        _filterUIState.update { update(it) }
    }

    private fun setLoadingComparisonState() {
        _competitionUIState.update {
            (it as? WorkoutCompetitionUIState.Loaded)?.copy(lapsComparisonData = LapsComparisonData(emptyList(), true)) ?: it
        }
    }

    private companion object {
        val SavedStateHandle.currentWorkoutHeader: WorkoutHeader
            get() = requireNotNull(get(WorkoutCompetitionActivity.REFERENCE_WORKOUT))
        val SavedStateHandle.workoutResult: CompetitionWorkoutSummary
            get() = requireNotNull(get(WorkoutCompetitionActivity.COMPETITION_WORKOUT_RESULT))
    }
}

sealed class WorkoutCompetitionUIState {
    data object Loading : WorkoutCompetitionUIState()
    data class Loaded(
        val usersProfile: Pair<UserProfile, UserProfile>,
        val competitionResult: CompetitionResultEnum?,
        val workoutComparisonUiState: WorkoutComparisonUiState.Loaded,
        val detailedComparisonList: List<DetailedComparison> = emptyList(),
        val lapsComparisonData: LapsComparisonData = LapsComparisonData(),
    ) : WorkoutCompetitionUIState()

    data class Error(
        val reason: CompetitionErrorEnum,
        @DrawableRes val activityIcon: Int,
        val activityGroup: ActivityGroup
    ) : WorkoutCompetitionUIState()
}

enum class SummarySelector {
    SUMMARY_DURATION,
    SUMMARY_DISTANCE,
    SUMMARY_PACE,
    SUMMARY_HEART_RATE
}

enum class CompetitionErrorEnum {
    DELETED_BY_USER,
    FORBIDDEN_BY_USER,
    LOAD_OTHER_WORKOUT_FAILED
}

enum class CompetitionResultEnum(val value: Int) {
    WINNER(1),
    LOSER(2),
    UNFINISHED(3),
}

data class LapsComparisonData(
    val values: List<Pair<LapsValueItem?, LapsValueItem?>> = emptyList(),
    val isLoading: Boolean = false
)

data class LapsComparisonFilter(
    val lapDistanceFilters: Map<LapsTable, String> = emptyMap(),
    val selectedLapsTable: LapsTable? = null,
    val selectedSummarySelector: SummarySelector = SummarySelector.SUMMARY_DURATION,
    val summarySelectors: List<SummarySelector> = emptyList()
)

data class UserProfile(
    val duration: String?,
    val displayedName: String?,
    val avatarUrl: String?,
    val isWinner: Boolean = false,
)

data class DetailedComparison(
    val title: String,
    val entryValue: Pair<String, String>,
    val entryUnitResEntry: Pair<Int?, Int?>,
)

data class LapsValueItem(
    val valueFormatted: String,
    val value: Float,
    var highlighted: Boolean = false
)
