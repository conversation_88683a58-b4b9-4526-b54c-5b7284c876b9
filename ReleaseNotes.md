#### 5.4.0 (2025-06-11)
 - Fixes
  - Should only cache reactions of own or friends' workouts
  - 184599,184601,184609,184610,184612,184616,184669 heart reate subpage bug
  - 185308 Combine reaction with local user info
  - 184637 Cadence graph is wrong for Dilu activities
  - Some padding on top is missing in Suunto plus store when searching
  - Fix some found bugs
  - Allow to continue without selecting additional sports when generating a plan [Develop]
  - 186034 test china env watch firmware check api
  - 184936 184603 several issues related to the summary cadence display
  - 181131 Failed to save competition mode for many competition target
  - Update the calendar tab, make it consistent with the style of dashboard
  - 183962 Can not disable/enable watch Wi-Fi
  - 184982 184985 184992 185010 185011 some bugs about tss widget page
  - some UI issues about training plan [develop]
  - 182363 183749  add incompatible category to the my guides page
  - <PERSON>perly checked if two sleep segments should be grouped in to the same sleep
  - 185434 Background color is wrong in SuuntoPlus Store view and it's sub-view when NG3 is connected [develop]
  - Remove the toggle of quick navigation [develop]
  - Update VO2Max WidgetInstruction
  - 184724,184751,184752,184753,184773,184807,184831,184672 resources subpage bug
  - 184480,184479 some detail page related bugs
  - Update totalDays is nullable
  - 184340,184187,184214,184301,184249,184363,184260,184362 HRV subpage bug
  - 184659,184664,184677,184684,184700,184581,184583
  - 183790 183808 183834 User profile bugs, 5/x
  - competition summary expression
  - 184101 185005 185131 User profile bugs, 4/x
  - Should show ZoneSense durations whenever possible
  - Fix issue by refactor sleep duration
  - Update the date in Summary table
  - Remove indoor check from hasRoute
  - 176692 177751 Default map zoom level for dives
  - 185007 185031 185089 185092 185094 185095 185097 185099 185102 185141 185143 185146 185208 User profile bugs, 3/x 
  - 184489 184504 184506 184513 184536 184578 184591 some issues about commute widget page
  - 185023 184990 185000 User profile bugs, 2/x
  - 184873,184950,184944 some tp bugs
  - Segmented control selection issue
  - 184477 184493 183882 183932 185009 User profile bugs, 1/x
  - 184524,184888,184896 some sleep bugs
  - 184258, 184268, 184384, 184344 Fixed several issues related to step widget sub page

 - Features
  - 177871 Update progress explanations 2
  - 186185 track the analytics events related to training plan
  - 185952 Update training zone structure, 2/x
  - 186063 Slide the statistics graph to right, 2/x
  - 184001 SU10 UI 2/x
  - 183774 Friends page, 13/x 
  - 185865 add support for setting my record as the target in competition mode
  - 185640 185649 185650 Activity card time stamp and sorting adjustment
  - Support App languages
  - 185869 New account deletion for SA-China, 2/x
  - Delete active plan when dismissing the warning if error is unknown [Develop]
  - 186063 Slide the statistics graph to right
  - Enable AI planner for fieldtesters
  - 177871 Update progress explanations
  - 185961 Vo2Max add new ChartGranularity
  - 185859 Refactor headset setting 2/x
  - 185952 Update training zone structure, 1/x
  - 184099 UI adjustments made on the device page
  - 185869 New account deletion for SA-China, 1/x
  - 185724 Move data analysis to new statistics
  - Confirm strings in friends view
  - remove chart detail string translatable
  - Add popular routes remote sync job
  - 185736 banner update [develop]
  - Routes pb params rename & some top rest api params rename
  - 184008 Metronome setting UI for SU10
  - 177869 Pagers replace View with Compose
  - 185543 Add more granularities to HR charts
  - Update default graph types for sleep comparison chart
  - 184502 recovery subpage, 5/x
  - 183774 Friends page, 12/x
  - 185544 Training data analysis graph, 3/x 
  - Update GenericSegmentedControl design
  - 185544 Training data analysis graph, 2/x 
  - 184502 recovery subpage, 4/x
  - 166880 Support ZoneSense in multisport activities, 2/2
  - 166880 Support ZoneSense in multisport activities, 1/x
  - 185544 Training data analysis graph, 1/x
  - 184502 recovery subpage, 3/x
  - 177871 Update Vo2Max calculations to use max values instead of averages
  - 185438 Update widget max count
  - 177859 Sleep design update 2
  - 175488 185224 New training zone, 18/x
  - 184502 - fix CalculateRecoveryScoreUseCase compile error
  - 177871 Progress design update 3
  - 184502 recovery subpage, 2/x
  - 185135 Update my route list item ui in library
  - 175488 New training zone, 17/x
  - 184502 recovery subpage, 1/x
  - 183774 Friends page, 11/x
  - 182902 Add top route detail ui

 - Technical
  - Prefer to use style URL fetched from remote
  - Remove FollowingActivity and related code
  - Update the name of the rws folder
  - Refactor map type related code, 7/x
  - Update sds to 3.32.5
  - New gradle settings
  - Refactor map type related code, 6/x
  - Refactor map types, 5/x
  - Update sds to 3.32.4 [develop]
  - More refactoring related to MapType
  - Clean-up code related to map types
  - Improve map type related code
  - Refactor headset setting UI 1/x
  - Use MapType.name as the unique key when setting map types
  - Relocate shared components to the Compose UI module
  - Introduce SuuntoTopBar
  - Align usage of icons, 2/x
  - Align usage of back button, 1/x
  - Use Duration in ZoneDurationData and friends
  - Use Duration for sleep duration, 3/x
  - Use Duration for sleep duration, 2/x
  - Use Duration for sleep duration, 1/x
  - Separate shareRouteEvent from BaseRouteListViewModel
  - Optimize TopRouteSortFilterScreen
  - Bump dependencies
  - Refactor TrendData.energy to be of type Energy
